/**
 * 测试forms和modals组件集成
 */

import React from 'react';
import { View } from 'tamagui';

// 测试forms组件导入
import { FormSelect, FormDatePicker, FormImagePicker } from './components/common/forms';

// 测试modals组件导入
import { PickerModal, CustomModal, ActionSheet } from './components/common/modals';

const TestComponent: React.FC = () => {
  return (
    <View>
      <FormSelect
        name="test"
        options={[]}
      />
      
      <FormDatePicker
        name="date"
      />
      
      <FormImagePicker
        name="image"
      />
      
      <PickerModal
        visible={false}
        onClose={() => {}}
        items={[]}
      />
      
      <CustomModal
        visible={false}
        onClose={() => {}}
      />
      
      <ActionSheet
        visible={false}
        onClose={() => {}}
        actions={[]}
      />
    </View>
  );
};

export default TestComponent;
