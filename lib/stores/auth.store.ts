/**
 * 用户认证状态管理
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, AuthState, LoginCredentials, BindAdminData } from '@/types/user';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  bindAdmin: (data: BindAdminData) => Promise<void>;
  logout: () => void;
  updateUser: (user: Partial<User>) => void;
  setToken: (token: string, refreshToken?: string) => void;
  clearError: () => void;
  
  // Loading states
  loginLoading: boolean;
  bindAdminLoading: boolean;
  
  // Error states
  loginError: string | null;
  bindAdminError: string | null;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      loginLoading: false,
      bindAdminLoading: false,
      loginError: null,
      bindAdminError: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        set({ loginLoading: true, loginError: null });
        
        try {
          // TODO: 替换为实际的API调用
          // const response = await authService.login(credentials);
          
          // Mock登录逻辑
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const mockUser: User = {
            id: 'user_001',
            name: '张管理员',
            phone: credentials.phone,
            role: 'admin',
            positions: ['housekeeper'],
            permissions: ['all'],
            accessRights: ['building_1', 'building_2'],
            buildingRights: ['building_1', 'building_2'],
            community: {
              id: 'community_001',
              name: '阳光花园小区'
            },
            registrationTime: new Date().toISOString(),
            lastLoginTime: new Date().toISOString(),
          };

          set({
            isAuthenticated: true,
            user: mockUser,
            token: 'mock_token_' + Date.now(),
            refreshToken: 'mock_refresh_token_' + Date.now(),
            loginLoading: false,
          });
        } catch (error) {
          set({
            loginLoading: false,
            loginError: error instanceof Error ? error.message : '登录失败',
          });
          throw error;
        }
      },

      bindAdmin: async (data: BindAdminData) => {
        set({ bindAdminLoading: true, bindAdminError: null });
        
        try {
          // TODO: 替换为实际的API调用
          // const response = await authService.bindAdmin(data);
          
          // Mock绑定逻辑
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          const mockUser: User = {
            id: 'admin_' + Date.now(),
            name: data.name,
            phone: data.phone,
            role: 'admin',
            positions: ['housekeeper'],
            permissions: ['all'],
            accessRights: ['building_1', 'building_2'],
            buildingRights: ['building_1', 'building_2'],
            community: {
              id: 'community_001',
              name: '阳光花园小区'
            },
            registrationTime: new Date().toISOString(),
          };

          set({
            isAuthenticated: true,
            user: mockUser,
            token: 'mock_token_' + Date.now(),
            refreshToken: 'mock_refresh_token_' + Date.now(),
            bindAdminLoading: false,
          });
        } catch (error) {
          set({
            bindAdminLoading: false,
            bindAdminError: error instanceof Error ? error.message : '绑定失败',
          });
          throw error;
        }
      },

      logout: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          refreshToken: null,
          loginError: null,
          bindAdminError: null,
        });
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData }
          });
        }
      },

      setToken: (token: string, refreshToken?: string) => {
        set({
          token,
          refreshToken: refreshToken || get().refreshToken,
        });
      },

      clearError: () => {
        set({
          loginError: null,
          bindAdminError: null,
        });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
      }),
    }
  )
);
