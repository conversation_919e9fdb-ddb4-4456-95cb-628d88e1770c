/**
 * 存储工具函数
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';

// 存储键名常量
export const STORAGE_KEYS = {
  // 用户相关
  USER_TOKEN: 'user_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  
  // 应用设置
  APP_SETTINGS: 'app_settings',
  THEME_MODE: 'theme_mode',
  LANGUAGE: 'language',
  
  // 缓存数据
  RESIDENTS_CACHE: 'residents_cache',
  STAFF_CACHE: 'staff_cache',
  NOTICES_CACHE: 'notices_cache',
  
  // 临时数据
  TEMP_DATA: 'temp_data',
  DRAFT_DATA: 'draft_data',
} as const;

/**
 * 普通存储操作（使用AsyncStorage）
 */
export const storage = {
  /**
   * 存储数据
   * @param key 键名
   * @param value 值
   */
  setItem: async (key: string, value: any): Promise<void> => {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (error) {
      console.error('Storage setItem error:', error);
      throw error;
    }
  },

  /**
   * 获取数据
   * @param key 键名
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  getItem: async <T = any>(key: string, defaultValue?: T): Promise<T | null> => {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      if (jsonValue === null) {
        return defaultValue || null;
      }
      return JSON.parse(jsonValue);
    } catch (error) {
      console.error('Storage getItem error:', error);
      return defaultValue || null;
    }
  },

  /**
   * 删除数据
   * @param key 键名
   */
  removeItem: async (key: string): Promise<void> => {
    try {
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Storage removeItem error:', error);
      throw error;
    }
  },

  /**
   * 清空所有数据
   */
  clear: async (): Promise<void> => {
    try {
      await AsyncStorage.clear();
    } catch (error) {
      console.error('Storage clear error:', error);
      throw error;
    }
  },

  /**
   * 获取所有键名
   * @returns 所有键名数组
   */
  getAllKeys: async (): Promise<string[]> => {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Storage getAllKeys error:', error);
      return [];
    }
  },

  /**
   * 批量获取数据
   * @param keys 键名数组
   * @returns 键值对数组
   */
  multiGet: async (keys: string[]): Promise<Array<[string, string | null]>> => {
    try {
      return await AsyncStorage.multiGet(keys);
    } catch (error) {
      console.error('Storage multiGet error:', error);
      return [];
    }
  },

  /**
   * 批量存储数据
   * @param keyValuePairs 键值对数组
   */
  multiSet: async (keyValuePairs: Array<[string, string]>): Promise<void> => {
    try {
      await AsyncStorage.multiSet(keyValuePairs);
    } catch (error) {
      console.error('Storage multiSet error:', error);
      throw error;
    }
  },

  /**
   * 批量删除数据
   * @param keys 键名数组
   */
  multiRemove: async (keys: string[]): Promise<void> => {
    try {
      await AsyncStorage.multiRemove(keys);
    } catch (error) {
      console.error('Storage multiRemove error:', error);
      throw error;
    }
  },
};

/**
 * 安全存储操作（使用SecureStore）
 */
export const secureStorage = {
  /**
   * 安全存储数据
   * @param key 键名
   * @param value 值
   */
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      await SecureStore.setItemAsync(key, value);
    } catch (error) {
      console.error('SecureStorage setItem error:', error);
      throw error;
    }
  },

  /**
   * 获取安全存储的数据
   * @param key 键名
   * @returns 存储的值
   */
  getItem: async (key: string): Promise<string | null> => {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      console.error('SecureStorage getItem error:', error);
      return null;
    }
  },

  /**
   * 删除安全存储的数据
   * @param key 键名
   */
  removeItem: async (key: string): Promise<void> => {
    try {
      await SecureStore.deleteItemAsync(key);
    } catch (error) {
      console.error('SecureStorage removeItem error:', error);
      throw error;
    }
  },

  /**
   * 检查键是否存在
   * @param key 键名
   * @returns 是否存在
   */
  hasItem: async (key: string): Promise<boolean> => {
    try {
      const value = await SecureStore.getItemAsync(key);
      return value !== null;
    } catch (error) {
      console.error('SecureStorage hasItem error:', error);
      return false;
    }
  },
};

/**
 * 缓存管理
 */
export const cache = {
  /**
   * 设置缓存（带过期时间）
   * @param key 键名
   * @param value 值
   * @param ttl 过期时间（秒）
   */
  set: async (key: string, value: any, ttl: number = 3600): Promise<void> => {
    const cacheData = {
      value,
      expiry: Date.now() + ttl * 1000,
    };
    await storage.setItem(key, cacheData);
  },

  /**
   * 获取缓存
   * @param key 键名
   * @param defaultValue 默认值
   * @returns 缓存的值或默认值
   */
  get: async <T = any>(key: string, defaultValue?: T): Promise<T | null> => {
    const cacheData = await storage.getItem(key);
    
    if (!cacheData || !cacheData.expiry) {
      return defaultValue || null;
    }
    
    // 检查是否过期
    if (Date.now() > cacheData.expiry) {
      await storage.removeItem(key);
      return defaultValue || null;
    }
    
    return cacheData.value;
  },

  /**
   * 删除缓存
   * @param key 键名
   */
  remove: async (key: string): Promise<void> => {
    await storage.removeItem(key);
  },

  /**
   * 清空所有缓存
   */
  clear: async (): Promise<void> => {
    const keys = await storage.getAllKeys();
    const cacheKeys = keys.filter(key => key.endsWith('_cache'));
    await storage.multiRemove(cacheKeys);
  },

  /**
   * 检查缓存是否存在且未过期
   * @param key 键名
   * @returns 是否存在有效缓存
   */
  has: async (key: string): Promise<boolean> => {
    const cacheData = await storage.getItem(key);
    
    if (!cacheData || !cacheData.expiry) {
      return false;
    }
    
    return Date.now() <= cacheData.expiry;
  },
};

/**
 * 用户数据存储
 */
export const userStorage = {
  /**
   * 保存用户token
   * @param token 访问token
   * @param refreshToken 刷新token
   */
  saveTokens: async (token: string, refreshToken: string): Promise<void> => {
    await secureStorage.setItem(STORAGE_KEYS.USER_TOKEN, token);
    await secureStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  },

  /**
   * 获取用户token
   * @returns 访问token
   */
  getToken: async (): Promise<string | null> => {
    return await secureStorage.getItem(STORAGE_KEYS.USER_TOKEN);
  },

  /**
   * 获取刷新token
   * @returns 刷新token
   */
  getRefreshToken: async (): Promise<string | null> => {
    return await secureStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
  },

  /**
   * 清除用户token
   */
  clearTokens: async (): Promise<void> => {
    await secureStorage.removeItem(STORAGE_KEYS.USER_TOKEN);
    await secureStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
  },

  /**
   * 保存用户信息
   * @param userInfo 用户信息
   */
  saveUserInfo: async (userInfo: any): Promise<void> => {
    await storage.setItem(STORAGE_KEYS.USER_INFO, userInfo);
  },

  /**
   * 获取用户信息
   * @returns 用户信息
   */
  getUserInfo: async (): Promise<any> => {
    return await storage.getItem(STORAGE_KEYS.USER_INFO);
  },

  /**
   * 清除用户信息
   */
  clearUserInfo: async (): Promise<void> => {
    await storage.removeItem(STORAGE_KEYS.USER_INFO);
  },

  /**
   * 清除所有用户数据
   */
  clearAll: async (): Promise<void> => {
    await Promise.all([
      userStorage.clearTokens(),
      userStorage.clearUserInfo(),
    ]);
  },
};
