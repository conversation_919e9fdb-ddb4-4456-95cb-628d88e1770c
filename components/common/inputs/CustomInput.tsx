/**
 * 自定义输入组件
 */

import React, { useState } from 'react';
import { Input, Text, View, XStack, YStack, styled } from 'tamagui';

// 输入组件属性
export interface CustomInputProps extends Omit<React.ComponentProps<typeof Input>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  size?: 'small' | 'medium' | 'large';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
}

// 样式化容器
const InputContainer = styled(YStack, {
  name: 'InputContainer',
  gap: '$2',
  width: '100%',
});

const LabelContainer = styled(XStack, {
  name: 'LabelContainer',
  alignItems: 'center',
  gap: '$1',
});

const InputLabel = styled(Text, {
  name: 'InputLabel',
  fontWeight: '500',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
    
    required: {
      true: {
        // 必填标记将通过子元素添加
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const RequiredMark = styled(Text, {
  name: 'RequiredMark',
  color: '$error',
  fontSize: '$4',
  fontWeight: 'bold',
});

const InputWrapper = styled(XStack, {
  name: 'InputWrapper',
  alignItems: 'center',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$3',
  backgroundColor: '$background',
  
  variants: {
    focused: {
      true: {
        borderColor: '$primary',
        shadowColor: '$primary',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
    },
    
    error: {
      true: {
        borderColor: '$error',
      },
    },
    
    disabled: {
      true: {
        backgroundColor: '$backgroundHover',
        opacity: 0.6,
      },
    },
    
    size: {
      small: {
        height: 36,
        paddingHorizontal: '$3',
      },
      
      medium: {
        height: 44,
        paddingHorizontal: '$4',
      },
      
      large: {
        height: 52,
        paddingHorizontal: '$5',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const StyledInput = styled(Input, {
  name: 'StyledInput',
  flex: 1,
  borderWidth: 0,
  backgroundColor: 'transparent',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const IconContainer = styled(View, {
  name: 'IconContainer',
  alignItems: 'center',
  justifyContent: 'center',

  variants: {
    iconPosition: {
      left: {
        marginRight: '$2',
      },

      right: {
        marginLeft: '$2',
      },
    },

    pressable: {
      true: {
        cursor: 'pointer',
      },
    },
  } as const,
});

const HelperText = styled(Text, {
  name: 'HelperText',
  
  variants: {
    error: {
      true: {
        color: '$error',
      },
      false: {
        color: '$placeholderColor',
      },
    },
    
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
    error: false,
  },
});

export const CustomInput: React.FC<CustomInputProps> = ({
  label,
  error,
  helperText,
  required = false,
  size = 'medium',
  leftIcon,
  rightIcon,
  onRightIconPress,
  disabled = false,
  onFocus,
  onBlur,
  ...props
}) => {
  const [focused, setFocused] = useState(false);

  const handleFocus = (event: any) => {
    setFocused(true);
    onFocus?.(event);
  };

  const handleBlur = (event: any) => {
    setFocused(false);
    onBlur?.(event);
  };

  const hasError = Boolean(error);
  const displayHelperText = error || helperText;

  return (
    <InputContainer>
      {label && (
        <LabelContainer>
          <InputLabel size={size} required={required}>
            {label}
          </InputLabel>
          {required && <RequiredMark>*</RequiredMark>}
        </LabelContainer>
      )}
      
      <InputWrapper
        size={size}
        focused={focused}
        error={hasError}
        disabled={disabled}
      >
        {leftIcon && (
          <IconContainer iconPosition="left">
            {leftIcon}
          </IconContainer>
        )}

        <StyledInput
          size={size}
          disabled={disabled}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholderTextColor="$placeholderColor"
          {...props}
        />

        {rightIcon && (
          <IconContainer
            iconPosition="right"
            pressable={Boolean(onRightIconPress)}
            onPress={onRightIconPress}
          >
            {rightIcon}
          </IconContainer>
        )}
      </InputWrapper>
      
      {displayHelperText && (
        <HelperText size={size} error={hasError}>
          {displayHelperText}
        </HelperText>
      )}
    </InputContainer>
  );
};
