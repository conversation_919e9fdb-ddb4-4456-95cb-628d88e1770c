/**
 * 密码输入组件
 */

import React, { useState } from 'react';
import { Text } from '@tamagui/core';
import { CustomInput, CustomInputProps } from './CustomInput';
import { IconButton } from '../buttons';

// 密码输入属性
export interface PasswordInputProps extends Omit<CustomInputProps, 'secureTextEntry' | 'rightIcon' | 'onRightIconPress'> {
  showStrengthIndicator?: boolean;
  strengthRules?: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  };
}

// 密码强度类型
type PasswordStrength = 'weak' | 'medium' | 'strong';

// 密码强度颜色映射
const strengthColors = {
  weak: '$error',
  medium: '$warning',
  strong: '$success',
};

// 密码强度文本映射
const strengthTexts = {
  weak: '弱',
  medium: '中',
  strong: '强',
};

export const PasswordInput: React.FC<PasswordInputProps> = ({
  showStrengthIndicator = false,
  strengthRules = {
    minLength: 6,
    requireUppercase: false,
    requireLowercase: false,
    requireNumbers: true,
    requireSpecialChars: false,
  },
  value = '',
  onChangeText,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [password, setPassword] = useState(value);

  // 切换密码可见性
  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  // 处理密码变化
  const handleChangeText = (text: string) => {
    setPassword(text);
    onChangeText?.(text);
  };

  // 计算密码强度
  const calculatePasswordStrength = (pwd: string): PasswordStrength => {
    let score = 0;
    
    // 长度检查
    if (pwd.length >= (strengthRules.minLength || 6)) {
      score += 1;
    }
    
    // 大写字母检查
    if (!strengthRules.requireUppercase || /[A-Z]/.test(pwd)) {
      score += 1;
    }
    
    // 小写字母检查
    if (!strengthRules.requireLowercase || /[a-z]/.test(pwd)) {
      score += 1;
    }
    
    // 数字检查
    if (!strengthRules.requireNumbers || /\d/.test(pwd)) {
      score += 1;
    }
    
    // 特殊字符检查
    if (!strengthRules.requireSpecialChars || /[!@#$%^&*(),.?":{}|<>]/.test(pwd)) {
      score += 1;
    }
    
    // 额外强度检查
    if (pwd.length >= 8) score += 1;
    if (pwd.length >= 12) score += 1;
    if (/[A-Z]/.test(pwd) && /[a-z]/.test(pwd)) score += 1;
    if (/\d/.test(pwd) && /[!@#$%^&*(),.?":{}|<>]/.test(pwd)) score += 1;
    
    if (score <= 3) return 'weak';
    if (score <= 6) return 'medium';
    return 'strong';
  };

  // 眼睛图标组件（简化版）
  const EyeIcon = ({ visible }: { visible: boolean }) => (
    <Text fontSize="$4">
      {visible ? '👁️' : '👁️‍🗨️'}
    </Text>
  );

  const passwordStrength = showStrengthIndicator ? calculatePasswordStrength(password) : null;

  return (
    <CustomInput
      {...props}
      value={password}
      onChangeText={handleChangeText}
      secureTextEntry={!isVisible}
      rightIcon={<EyeIcon visible={isVisible} />}
      onRightIconPress={toggleVisibility}
      helperText={
        showStrengthIndicator && password.length > 0
          ? `密码强度: ${strengthTexts[passwordStrength!]}`
          : props.helperText
      }
    />
  );
};
