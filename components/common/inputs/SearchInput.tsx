/**
 * 搜索输入组件
 */

import React, { useState, useCallback } from 'react';
import { Input, XStack, View, Text } from '@tamagui/core';
import { styled } from '@tamagui/core';
import { IconButton } from '../buttons';

// 搜索输入属性
export interface SearchInputProps {
  value?: string;
  placeholder?: string;
  onSearch?: (value: string) => void;
  onClear?: () => void;
  onChangeText?: (text: string) => void;
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  autoFocus?: boolean;
  debounceMs?: number;
}

// 样式化搜索容器
const SearchContainer = styled(XStack, {
  name: 'SearchContainer',
  alignItems: 'center',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$6',
  backgroundColor: '$backgroundHover',
  
  variants: {
    focused: {
      true: {
        borderColor: '$primary',
        backgroundColor: '$background',
      },
    },
    
    disabled: {
      true: {
        opacity: 0.6,
      },
    },
    
    size: {
      small: {
        height: 36,
        paddingHorizontal: '$3',
      },
      
      medium: {
        height: 44,
        paddingHorizontal: '$4',
      },
      
      large: {
        height: 52,
        paddingHorizontal: '$5',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const SearchIcon = styled(View, {
  name: 'SearchIcon',
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: '$2',
  opacity: 0.6,
});

const StyledSearchInput = styled(Input, {
  name: 'StyledSearchInput',
  flex: 1,
  borderWidth: 0,
  backgroundColor: 'transparent',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const ClearButton = styled(View, {
  name: 'ClearButton',
  marginLeft: '$2',
});

// 防抖Hook
const useDebounce = (callback: (value: string) => void, delay: number) => {
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const debouncedCallback = useCallback((value: string) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    const timer = setTimeout(() => {
      callback(value);
    }, delay);

    setDebounceTimer(timer);
  }, [callback, delay, debounceTimer]);

  return debouncedCallback;
};

export const SearchInput: React.FC<SearchInputProps> = ({
  value = '',
  placeholder = '搜索...',
  onSearch,
  onClear,
  onChangeText,
  size = 'medium',
  disabled = false,
  autoFocus = false,
  debounceMs = 300,
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [focused, setFocused] = useState(false);

  // 防抖搜索
  const debouncedSearch = useDebounce((searchValue: string) => {
    onSearch?.(searchValue);
  }, debounceMs);

  const handleChangeText = (text: string) => {
    setInputValue(text);
    onChangeText?.(text);
    
    if (onSearch) {
      debouncedSearch(text);
    }
  };

  const handleClear = () => {
    setInputValue('');
    onChangeText?.('');
    onClear?.();
    onSearch?.('');
  };

  const handleFocus = () => {
    setFocused(true);
  };

  const handleBlur = () => {
    setFocused(false);
  };

  // 搜索图标（简化版，实际项目中应使用图标库）
  const SearchIconComponent = () => (
    <Text fontSize={size === 'small' ? '$3' : size === 'large' ? '$5' : '$4'}>
      🔍
    </Text>
  );

  // 清除图标
  const ClearIconComponent = () => (
    <Text fontSize={size === 'small' ? '$3' : size === 'large' ? '$5' : '$4'}>
      ✕
    </Text>
  );

  return (
    <SearchContainer
      size={size}
      focused={focused}
      disabled={disabled}
    >
      <SearchIcon>
        <SearchIconComponent />
      </SearchIcon>
      
      <StyledSearchInput
        size={size}
        value={inputValue}
        placeholder={placeholder}
        placeholderTextColor="$placeholderColor"
        onChangeText={handleChangeText}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        autoFocus={autoFocus}
        returnKeyType="search"
        onSubmitEditing={() => onSearch?.(inputValue)}
      />
      
      {inputValue.length > 0 && (
        <ClearButton>
          <IconButton
            icon={<ClearIconComponent />}
            size={size === 'small' ? 'small' : 'medium'}
            variant="ghost"
            onPress={handleClear}
          />
        </ClearButton>
      )}
    </SearchContainer>
  );
};
