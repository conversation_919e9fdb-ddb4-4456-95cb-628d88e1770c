/**
 * 图标按钮组件
 */

import React from 'react';
import { Button, ButtonProps } from '@tamagui/core';
import { styled } from '@tamagui/core';

// 图标按钮尺寸类型
export type IconButtonSize = 'small' | 'medium' | 'large';

// 图标按钮变体类型
export type IconButtonVariant = 'default' | 'primary' | 'secondary' | 'danger' | 'ghost';

// 图标按钮属性
export interface IconButtonProps extends Omit<ButtonProps, 'size' | 'variant'> {
  icon: React.ReactNode;
  size?: IconButtonSize;
  variant?: IconButtonVariant;
  disabled?: boolean;
  loading?: boolean;
}

// 样式化图标按钮
const StyledIconButton = styled(Button, {
  name: 'IconButton',
  borderRadius: '$4',
  cursor: 'pointer',
  alignItems: 'center',
  justifyContent: 'center',
  
  variants: {
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },
    
    variant: {
      default: {
        backgroundColor: '$backgroundHover',
        color: '$color',
        borderColor: '$borderColor',
        
        hoverStyle: {
          backgroundColor: '$backgroundPress',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
          scale: 0.95,
        },
      },
      
      primary: {
        backgroundColor: '$primary',
        color: '$white',
        borderColor: '$primary',
        
        hoverStyle: {
          backgroundColor: '$primaryHover',
        },
        
        pressStyle: {
          backgroundColor: '$primaryHover',
          scale: 0.95,
        },
      },
      
      secondary: {
        backgroundColor: '$secondary',
        color: '$white',
        borderColor: '$secondary',
        
        hoverStyle: {
          backgroundColor: '$secondaryHover',
        },
        
        pressStyle: {
          backgroundColor: '$secondaryHover',
          scale: 0.95,
        },
      },
      
      danger: {
        backgroundColor: '$error',
        color: '$white',
        borderColor: '$error',
        
        hoverStyle: {
          backgroundColor: '$errorHover',
        },
        
        pressStyle: {
          backgroundColor: '$errorHover',
          scale: 0.95,
        },
      },
      
      ghost: {
        backgroundColor: 'transparent',
        color: '$color',
        borderColor: 'transparent',
        
        hoverStyle: {
          backgroundColor: '$backgroundHover',
        },
        
        pressStyle: {
          backgroundColor: '$backgroundPress',
          scale: 0.95,
        },
      },
    },
    
    size: {
      small: {
        width: 32,
        height: 32,
      },
      
      medium: {
        width: 40,
        height: 40,
      },
      
      large: {
        width: 48,
        height: 48,
      },
    },
  },
  
  defaultVariants: {
    variant: 'default',
    size: 'medium',
  },
});

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  size = 'medium',
  variant = 'default',
  disabled = false,
  loading = false,
  onPress,
  ...props
}) => {
  const handlePress = (event: any) => {
    if (loading || disabled) {
      return;
    }
    onPress?.(event);
  };

  return (
    <StyledIconButton
      variant={variant}
      size={size}
      disabled={disabled || loading}
      onPress={handlePress}
      {...props}
    >
      {icon}
    </StyledIconButton>
  );
};
