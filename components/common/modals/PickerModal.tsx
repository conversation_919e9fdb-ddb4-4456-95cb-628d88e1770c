/**
 * 选择器模态框组件
 * 提供单选和多选功能的选择器
 */

import React, { useState, useEffect } from 'react';
import { YStack, XStack, Text, styled, ScrollView } from 'tamagui';
import { CustomModal, CustomModalProps } from './CustomModal';
import { CustomButton } from '../buttons';
import { CustomInput } from '../inputs';

// 选择项数据类型
export interface PickerItem {
  key: string;
  label: string;
  value: any;
  disabled?: boolean;
  description?: string;
  icon?: React.ReactNode;
}

// 选择器模态框组件属性
export interface PickerModalProps extends Omit<CustomModalProps, 'children' | 'footer'> {
  items: PickerItem[];
  selectedKeys?: string[];
  defaultSelectedKeys?: string[];
  onSelectionChange?: (selectedKeys: string[], selectedItems: PickerItem[]) => void;
  onConfirm?: (selectedKeys: string[], selectedItems: PickerItem[]) => void;
  onCancel?: () => void;
  multiple?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
  confirmText?: string;
  cancelText?: string;
  emptyText?: string;
  maxSelection?: number;
}

// 样式化选择器容器
const PickerContainer = styled(YStack, {
  name: 'PickerContainer',
  flex: 1,
  maxHeight: 400,
});

const SearchContainer = styled(YStack, {
  name: 'SearchContainer',
  padding: '$3',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
});

const ItemList = styled(ScrollView, {
  name: 'ItemList',
  flex: 1,
});

const PickerItem = styled(XStack, {
  name: 'PickerItem',
  alignItems: 'center',
  paddingHorizontal: '$4',
  paddingVertical: '$3',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
  cursor: 'pointer',
  
  variants: {
    selected: {
      true: {
        backgroundColor: '$primaryHover',
      },
    },
    
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
      },
    },
  } as const,
  
  hoverStyle: {
    backgroundColor: '$backgroundHover',
  },
  
  pressStyle: {
    backgroundColor: '$backgroundPress',
  },
});

const ItemIcon = styled(YStack, {
  name: 'ItemIcon',
  alignItems: 'center',
  justifyContent: 'center',
  width: 24,
  height: 24,
  marginRight: '$3',
});

const ItemContent = styled(YStack, {
  name: 'ItemContent',
  flex: 1,
});

const ItemLabel = styled(Text, {
  name: 'ItemLabel',
  fontSize: '$4',
  fontWeight: '500',
  color: '$color',
});

const ItemDescription = styled(Text, {
  name: 'ItemDescription',
  fontSize: '$3',
  color: '$placeholderColor',
  marginTop: '$1',
});

const SelectionIndicator = styled(YStack, {
  name: 'SelectionIndicator',
  alignItems: 'center',
  justifyContent: 'center',
  width: 20,
  height: 20,
  borderRadius: '$2',
  borderWidth: 2,
  borderColor: '$borderColor',
  
  variants: {
    selected: {
      true: {
        backgroundColor: '$primary',
        borderColor: '$primary',
      },
    },
    
    multiple: {
      true: {
        borderRadius: '$1',
      },
      false: {
        borderRadius: '$6',
      },
    },
  } as const,
});

const CheckIcon = styled(Text, {
  name: 'CheckIcon',
  color: '$white',
  fontSize: '$3',
  fontWeight: 'bold',
});

const EmptyState = styled(YStack, {
  name: 'EmptyState',
  alignItems: 'center',
  justifyContent: 'center',
  padding: '$6',
});

const EmptyText = styled(Text, {
  name: 'EmptyText',
  fontSize: '$3',
  color: '$placeholderColor',
  textAlign: 'center',
});

const ButtonContainer = styled(XStack, {
  name: 'ButtonContainer',
  gap: '$3',
});

export const PickerModal: React.FC<PickerModalProps> = ({
  items,
  selectedKeys: controlledSelectedKeys,
  defaultSelectedKeys = [],
  onSelectionChange,
  onConfirm,
  onCancel,
  onClose,
  multiple = false,
  searchable = false,
  searchPlaceholder = '搜索...',
  confirmText = '确认',
  cancelText = '取消',
  emptyText = '暂无选项',
  maxSelection,
  ...modalProps
}) => {
  const [internalSelectedKeys, setInternalSelectedKeys] = useState<string[]>(
    controlledSelectedKeys || defaultSelectedKeys
  );
  const [searchText, setSearchText] = useState('');

  const selectedKeys = controlledSelectedKeys || internalSelectedKeys;

  useEffect(() => {
    if (controlledSelectedKeys) {
      setInternalSelectedKeys(controlledSelectedKeys);
    }
  }, [controlledSelectedKeys]);

  // 过滤项目
  const filteredItems = items.filter(item =>
    item.label.toLowerCase().includes(searchText.toLowerCase()) ||
    item.description?.toLowerCase().includes(searchText.toLowerCase())
  );

  // 处理选择
  const handleItemPress = (item: PickerItem) => {
    if (item.disabled) return;

    let newSelectedKeys: string[];

    if (multiple) {
      if (selectedKeys.includes(item.key)) {
        newSelectedKeys = selectedKeys.filter(key => key !== item.key);
      } else {
        if (maxSelection && selectedKeys.length >= maxSelection) {
          return; // 达到最大选择数量
        }
        newSelectedKeys = [...selectedKeys, item.key];
      }
    } else {
      newSelectedKeys = selectedKeys.includes(item.key) ? [] : [item.key];
    }

    if (controlledSelectedKeys === undefined) {
      setInternalSelectedKeys(newSelectedKeys);
    }

    const selectedItems = items.filter(item => newSelectedKeys.includes(item.key));
    onSelectionChange?.(newSelectedKeys, selectedItems);
  };

  // 处理确认
  const handleConfirm = () => {
    const selectedItems = items.filter(item => selectedKeys.includes(item.key));
    onConfirm?.(selectedKeys, selectedItems);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    onCancel?.();
    onClose();
  };

  const footer = (
    <ButtonContainer>
      <CustomButton
        variant="primary"
        size="medium"
        onPress={handleConfirm}
        flex={1}
      >
        {confirmText}
      </CustomButton>
      
      <CustomButton
        variant="outline"
        size="medium"
        onPress={handleCancel}
        flex={1}
      >
        {cancelText}
      </CustomButton>
    </ButtonContainer>
  );

  return (
    <CustomModal
      {...modalProps}
      onClose={onClose}
      size="medium"
      position="center"
      footer={footer}
    >
      <PickerContainer>
        {searchable && (
          <SearchContainer>
            <CustomInput
              placeholder={searchPlaceholder}
              value={searchText}
              onChangeText={setSearchText}
              size="medium"
            />
          </SearchContainer>
        )}
        
        <ItemList showsVerticalScrollIndicator={false}>
          {filteredItems.length > 0 ? (
            filteredItems.map((item) => (
              <PickerItem
                key={item.key}
                selected={selectedKeys.includes(item.key)}
                disabled={item.disabled}
                onPress={() => handleItemPress(item)}
              >
                {item.icon && (
                  <ItemIcon>
                    {item.icon}
                  </ItemIcon>
                )}
                
                <ItemContent>
                  <ItemLabel>
                    {item.label}
                  </ItemLabel>
                  
                  {item.description && (
                    <ItemDescription>
                      {item.description}
                    </ItemDescription>
                  )}
                </ItemContent>
                
                <SelectionIndicator
                  selected={selectedKeys.includes(item.key)}
                  multiple={multiple}
                >
                  {selectedKeys.includes(item.key) && (
                    <CheckIcon>
                      {multiple ? '✓' : '●'}
                    </CheckIcon>
                  )}
                </SelectionIndicator>
              </PickerItem>
            ))
          ) : (
            <EmptyState>
              <EmptyText>
                {emptyText}
              </EmptyText>
            </EmptyState>
          )}
        </ItemList>
      </PickerContainer>
    </CustomModal>
  );
};
