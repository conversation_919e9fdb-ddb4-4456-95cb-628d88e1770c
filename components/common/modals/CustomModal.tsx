/**
 * 自定义模态框组件
 * 基于Tamagui的Sheet组件实现
 */

import React, { useEffect } from 'react';
import { Sheet, Text, XStack, YStack, styled } from 'tamagui';
import { IconButton } from '../buttons';

// 模态框组件属性
export interface CustomModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  children?: React.ReactNode;
  footer?: React.ReactNode;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  position?: 'center' | 'bottom' | 'top';
  closable?: boolean;
  maskClosable?: boolean;
  animationType?: 'slide' | 'fade' | 'none';
  onShow?: () => void;
  onHide?: () => void;
}

// 样式化模态框容器
const ModalContainer = styled(YStack, {
  name: 'ModalContainer',
  backgroundColor: '$background',
  borderRadius: '$4',
  overflow: 'hidden',
  maxHeight: '90%',
  
  variants: {
    size: {
      small: {
        width: '80%',
        maxWidth: 320,
      },
      
      medium: {
        width: '90%',
        maxWidth: 480,
      },
      
      large: {
        width: '95%',
        maxWidth: 640,
      },
      
      fullscreen: {
        width: '100%',
        height: '100%',
        borderRadius: 0,
        maxHeight: '100%',
      },
    },
    
    position: {
      center: {
        // 居中样式由Sheet处理
      },
      
      bottom: {
        borderTopLeftRadius: '$4',
        borderTopRightRadius: '$4',
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
      },
      
      top: {
        borderTopLeftRadius: 0,
        borderTopRightRadius: 0,
        borderBottomLeftRadius: '$4',
        borderBottomRightRadius: '$4',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
    position: 'center',
  },
});

const ModalHeader = styled(XStack, {
  name: 'ModalHeader',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '$4',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
  
  variants: {
    closable: {
      true: {
        paddingRight: '$3',
      },
    },
  } as const,
});

const ModalContent = styled(YStack, {
  name: 'ModalContent',
  flex: 1,
  padding: '$4',
});

const ModalFooter = styled(XStack, {
  name: 'ModalFooter',
  alignItems: 'center',
  justifyContent: 'flex-end',
  padding: '$4',
  borderTopWidth: 1,
  borderTopColor: '$borderColor',
  gap: '$3',
});

const ModalTitle = styled(Text, {
  name: 'ModalTitle',
  fontSize: '$5',
  fontWeight: '600',
  color: '$color',
  flex: 1,
});

const ModalSubtitle = styled(Text, {
  name: 'ModalSubtitle',
  fontSize: '$3',
  color: '$placeholderColor',
  marginTop: '$1',
});

export const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  onClose,
  title,
  subtitle,
  children,
  footer,
  size = 'medium',
  position = 'center',
  closable = true,
  maskClosable = true,
  animationType = 'slide',
  onShow,
  onHide,
}) => {
  useEffect(() => {
    if (visible) {
      onShow?.();
    } else {
      onHide?.();
    }
  }, [visible, onShow, onHide]);

  const hasHeader = Boolean(title || subtitle || closable);

  return (
    <Sheet
      modal
      open={visible}
      onOpenChange={(open: boolean) => {
        if (!open) {
          onClose();
        }
      }}
      snapPoints={position === 'center' ? [85] : [50, 85]}
      position={position === 'bottom' ? 0 : undefined}
      dismissOnSnapToBottom={position === 'bottom'}
      dismissOnOverlayPress={maskClosable}
      animation={animationType === 'none' ? undefined : 'medium'}
    >
      <Sheet.Overlay 
        backgroundColor="rgba(0, 0, 0, 0.5)"
        enterStyle={{ opacity: 0 }}
        exitStyle={{ opacity: 0 }}
      />
      
      <Sheet.Handle />
      
      <Sheet.Frame
        flex={1}
        justifyContent={position === 'center' ? 'center' : 'flex-end'}
        alignItems="center"
        padding={position === 'center' ? '$4' : '$0'}
      >
        <ModalContainer size={size} position={position as any}>
          {hasHeader && (
            <ModalHeader closable={closable}>
              <YStack flex={1}>
                {title && (
                  <ModalTitle>
                    {title}
                  </ModalTitle>
                )}
                {subtitle && (
                  <ModalSubtitle>
                    {subtitle}
                  </ModalSubtitle>
                )}
              </YStack>
              
              {closable && (
                <IconButton
                  icon={<Text>✕</Text>}
                  variant="ghost"
                  size="small"
                  onPress={onClose}
                />
              )}
            </ModalHeader>
          )}
          
          <ModalContent>
            {children}
          </ModalContent>
          
          {footer && (
            <ModalFooter>
              {footer}
            </ModalFooter>
          )}
        </ModalContainer>
      </Sheet.Frame>
    </Sheet>
  );
};
