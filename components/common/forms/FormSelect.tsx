/**
 * 表单选择器组件
 * 基于PickerModal实现的表单选择器
 */

import React, { useState, useEffect } from 'react';
import { XStack, YStack, Text, styled } from 'tamagui';
import { PickerModal, PickerItem } from '../modals';
import { ValidationRule } from './FormInput';

// 表单选择器组件属性
export interface FormSelectProps {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  multiple?: boolean;
  searchable?: boolean;
  options: PickerItem[];
  value?: string | string[];
  defaultValue?: string | string[];
  onSelectionChange?: (value: string | string[], selectedItems: PickerItem[]) => void;
  rules?: ValidationRule[];
  validateTrigger?: 'onChange' | 'onBlur' | 'onSubmit';
  showErrorMessage?: boolean;
  onValidationChange?: (name: string, isValid: boolean, errorMessage?: string) => void;
  form?: any;
  size?: 'small' | 'medium' | 'large';
  maxSelection?: number;
  emptyText?: string;
}

// 样式化选择器容器
const SelectContainer = styled(YStack, {
  name: 'SelectContainer',
  gap: '$2',
});

const SelectLabel = styled(Text, {
  name: 'SelectLabel',
  fontSize: '$4',
  fontWeight: '500',
  color: '$color',
  
  variants: {
    required: {
      true: {
        // 必填标记将通过伪元素添加
      },
    },
    
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const SelectTrigger = styled(XStack, {
  name: 'SelectTrigger',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$3',
  backgroundColor: '$background',
  cursor: 'pointer',
  
  variants: {
    size: {
      small: {
        paddingHorizontal: '$3',
        paddingVertical: '$2',
        minHeight: 36,
      },
      
      medium: {
        paddingHorizontal: '$4',
        paddingVertical: '$3',
        minHeight: 44,
      },
      
      large: {
        paddingHorizontal: '$5',
        paddingVertical: '$4',
        minHeight: 52,
      },
    },
    
    disabled: {
      true: {
        opacity: 0.5,
        cursor: 'not-allowed',
        backgroundColor: '$backgroundHover',
      },
    },
    
    error: {
      true: {
        borderColor: '$error',
      },
    },
    
    focused: {
      true: {
        borderColor: '$primary',
        shadowColor: '$primary',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
  
  hoverStyle: {
    borderColor: '$borderColorHover',
  },
  
  pressStyle: {
    backgroundColor: '$backgroundPress',
  },
});

const SelectValue = styled(Text, {
  name: 'SelectValue',
  flex: 1,
  color: '$color',
  
  variants: {
    placeholder: {
      true: {
        color: '$placeholderColor',
      },
    },
    
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const SelectIcon = styled(Text, {
  name: 'SelectIcon',
  color: '$placeholderColor',
  marginLeft: '$2',
  
  variants: {
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  } as const,
  
  defaultVariants: {
    size: 'medium',
  },
});

const ErrorMessage = styled(Text, {
  name: 'ErrorMessage',
  fontSize: '$3',
  color: '$error',
  marginTop: '$1',
  lineHeight: '$3',
});

const RequiredMark = styled(Text, {
  name: 'RequiredMark',
  color: '$error',
  marginLeft: '$1',
});

export const FormSelect: React.FC<FormSelectProps> = ({
  name,
  label,
  placeholder = '请选择',
  required = false,
  disabled = false,
  multiple = false,
  searchable = false,
  options,
  value: controlledValue,
  defaultValue,
  onSelectionChange,
  rules = [],
  validateTrigger = 'onChange',
  showErrorMessage = true,
  onValidationChange,
  form,
  size = 'medium',
  maxSelection,
  emptyText = '暂无选项',
}) => {
  const [internalValue, setInternalValue] = useState<string | string[]>(
    controlledValue || defaultValue || (multiple ? [] : '')
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isValid, setIsValid] = useState(true);
  const [touched, setTouched] = useState(false);
  const [focused, setFocused] = useState(false);

  const currentValue = controlledValue !== undefined ? controlledValue : internalValue;

  // 验证函数
  const validateValue = (value: string | string[]): { isValid: boolean; errorMessage: string } => {
    // 合并必填规则
    const allRules = required ? [{ required: true, message: '请选择一个选项' }, ...rules] : rules;
    
    for (const rule of allRules) {
      // 必填验证
      if (rule.required) {
        const isEmpty = multiple 
          ? Array.isArray(value) && value.length === 0
          : !value || value === '';
        
        if (isEmpty) {
          return {
            isValid: false,
            errorMessage: rule.message || '请选择一个选项',
          };
        }
      }

      // 自定义验证
      if (rule.validator) {
        const stringValue = Array.isArray(value) ? value.join(',') : String(value);
        const result = rule.validator(stringValue);
        if (result !== true) {
          return {
            isValid: false,
            errorMessage: typeof result === 'string' ? result : (rule.message || '验证失败'),
          };
        }
      }
    }

    return { isValid: true, errorMessage: '' };
  };

  // 执行验证
  const performValidation = (value: string | string[]) => {
    const { isValid: valid, errorMessage: error } = validateValue(value);
    
    setIsValid(valid);
    setErrorMessage(error);
    
    // 通知父组件验证结果
    onValidationChange?.(name, valid, error);
    
    // 如果有表单实例，更新表单状态
    if (form) {
      form.setFieldValidation?.(name, valid, error);
    }
  };

  // 处理选择变化
  const handleSelectionChange = (selectedKeys: string[], selectedItems: PickerItem[]) => {
    const newValue = multiple ? selectedKeys : selectedKeys[0] || '';
    
    if (controlledValue === undefined) {
      setInternalValue(newValue);
    }
    
    onSelectionChange?.(newValue, selectedItems);
    
    // 如果验证触发器是onChange，立即验证
    if (validateTrigger === 'onChange') {
      performValidation(newValue);
    }
  };

  // 处理模态框关闭
  const handleModalClose = () => {
    setModalVisible(false);
    setFocused(false);
    setTouched(true);
    
    // 如果验证触发器是onBlur，执行验证
    if (validateTrigger === 'onBlur') {
      performValidation(currentValue);
    }
  };

  // 处理点击
  const handlePress = () => {
    if (disabled) return;
    
    setModalVisible(true);
    setFocused(true);
  };

  // 获取显示文本
  const getDisplayText = (): string => {
    if (multiple && Array.isArray(currentValue)) {
      if (currentValue.length === 0) return placeholder;
      
      const selectedItems = options.filter(option => currentValue.includes(option.key));
      return selectedItems.map(item => item.label).join(', ');
    } else {
      if (!currentValue) return placeholder;
      
      const selectedItem = options.find(option => option.key === currentValue);
      return selectedItem?.label || placeholder;
    }
  };

  // 获取选中的keys
  const getSelectedKeys = (): string[] => {
    if (multiple && Array.isArray(currentValue)) {
      return currentValue;
    } else if (currentValue) {
      return [String(currentValue)];
    }
    return [];
  };

  const displayText = getDisplayText();
  const isPlaceholder = displayText === placeholder;
  const showError = touched && !isValid && showErrorMessage;

  // 暴露验证方法给父组件
  useEffect(() => {
    if (form) {
      form.registerField?.(name, {
        validate: () => performValidation(currentValue),
        getValue: () => currentValue,
        setValue: (value: string | string[]) => {
          if (controlledValue === undefined) {
            setInternalValue(value);
          }
        },
      });
    }
  }, [name, form, currentValue, controlledValue]);

  return (
    <SelectContainer>
      {label && (
        <XStack alignItems="center">
          <SelectLabel size={size} required={required}>
            {label}
          </SelectLabel>
          {required && <RequiredMark>*</RequiredMark>}
        </XStack>
      )}
      
      <SelectTrigger
        size={size}
        disabled={disabled}
        error={showError}
        focused={focused}
        onPress={handlePress}
      >
        <SelectValue
          size={size}
          placeholder={isPlaceholder}
          numberOfLines={1}
        >
          {displayText}
        </SelectValue>
        
        <SelectIcon size={size}>
          {modalVisible ? '▲' : '▼'}
        </SelectIcon>
      </SelectTrigger>
      
      {showError && errorMessage && (
        <ErrorMessage>
          {errorMessage}
        </ErrorMessage>
      )}
      
      <PickerModal
        visible={modalVisible}
        onClose={handleModalClose}
        title={label || '请选择'}
        items={options}
        selectedKeys={getSelectedKeys()}
        onSelectionChange={handleSelectionChange}
        multiple={multiple}
        searchable={searchable}
        maxSelection={maxSelection}
        emptyText={emptyText}
      />
    </SelectContainer>
  );
};
