/**
 * 状态徽章组件
 */

import React from 'react';
import { View, Text } from '@tamagui/core';
import { styled } from '@tamagui/core';

// 状态类型
export type BadgeStatus = 'success' | 'warning' | 'error' | 'info' | 'default';

// 徽章尺寸类型
export type BadgeSize = 'small' | 'medium' | 'large';

// 状态徽章属性
export interface StatusBadgeProps {
  status?: BadgeStatus;
  size?: BadgeSize;
  text: string;
  icon?: React.ReactNode;
  dot?: boolean;
}

// 样式化徽章容器
const BadgeContainer = styled(View, {
  name: 'BadgeContainer',
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '$2',
  
  variants: {
    status: {
      success: {
        backgroundColor: '$success',
      },
      
      warning: {
        backgroundColor: '$warning',
      },
      
      error: {
        backgroundColor: '$error',
      },
      
      info: {
        backgroundColor: '$primary',
      },
      
      default: {
        backgroundColor: '$backgroundHover',
      },
    },
    
    size: {
      small: {
        paddingHorizontal: '$2',
        paddingVertical: '$1',
        gap: '$1',
      },
      
      medium: {
        paddingHorizontal: '$3',
        paddingVertical: '$1.5',
        gap: '$1.5',
      },
      
      large: {
        paddingHorizontal: '$4',
        paddingVertical: '$2',
        gap: '$2',
      },
    },
    
    dot: {
      true: {
        paddingHorizontal: '$1',
        paddingVertical: '$1',
        borderRadius: '$12',
        minWidth: 8,
        minHeight: 8,
      },
    },
  },
  
  defaultVariants: {
    status: 'default',
    size: 'medium',
  },
});

const BadgeText = styled(Text, {
  name: 'BadgeText',
  fontWeight: '500',
  
  variants: {
    status: {
      success: {
        color: '$white',
      },
      
      warning: {
        color: '$white',
      },
      
      error: {
        color: '$white',
      },
      
      info: {
        color: '$white',
      },
      
      default: {
        color: '$color',
      },
    },
    
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  },
  
  defaultVariants: {
    status: 'default',
    size: 'medium',
  },
});

const DotIndicator = styled(View, {
  name: 'DotIndicator',
  borderRadius: '$12',
  
  variants: {
    status: {
      success: {
        backgroundColor: '$success',
      },
      
      warning: {
        backgroundColor: '$warning',
      },
      
      error: {
        backgroundColor: '$error',
      },
      
      info: {
        backgroundColor: '$primary',
      },
      
      default: {
        backgroundColor: '$placeholderColor',
      },
    },
    
    size: {
      small: {
        width: 6,
        height: 6,
      },
      
      medium: {
        width: 8,
        height: 8,
      },
      
      large: {
        width: 10,
        height: 10,
      },
    },
  },
  
  defaultVariants: {
    status: 'default',
    size: 'medium',
  },
});

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status = 'default',
  size = 'medium',
  text,
  icon,
  dot = false,
}) => {
  if (dot) {
    return (
      <BadgeContainer status={status} size={size} dot={dot}>
        <DotIndicator status={status} size={size} />
      </BadgeContainer>
    );
  }

  return (
    <BadgeContainer status={status} size={size}>
      {icon}
      <BadgeText status={status} size={size}>
        {text}
      </BadgeText>
    </BadgeContainer>
  );
};
