/**
 * 加载指示器组件
 */

import React from 'react';
import { View, Spinner, Text, YStack } from '@tamagui/core';
import { styled } from '@tamagui/core';

// 加载指示器尺寸类型
export type LoadingSize = 'small' | 'medium' | 'large';

// 加载指示器属性
export interface LoadingSpinnerProps {
  size?: LoadingSize;
  color?: string;
  text?: string;
  overlay?: boolean;
  visible?: boolean;
}

// 样式化容器
const LoadingContainer = styled(YStack, {
  name: 'LoadingContainer',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '$3',
  
  variants: {
    overlay: {
      true: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 1000,
      },
    },
    
    size: {
      small: {
        padding: '$2',
      },
      
      medium: {
        padding: '$4',
      },
      
      large: {
        padding: '$6',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const LoadingText = styled(Text, {
  name: 'LoadingText',
  textAlign: 'center',
  
  variants: {
    overlay: {
      true: {
        color: '$white',
      },
      false: {
        color: '$color',
      },
    },
    
    size: {
      small: {
        fontSize: '$3',
      },
      
      medium: {
        fontSize: '$4',
      },
      
      large: {
        fontSize: '$5',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
    overlay: false,
  },
});

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color,
  text,
  overlay = false,
  visible = true,
}) => {
  if (!visible) {
    return null;
  }

  const spinnerSize = size === 'small' ? 'small' : size === 'large' ? 'large' : 'medium';
  const spinnerColor = color || (overlay ? '$white' : '$primary');

  return (
    <LoadingContainer size={size} overlay={overlay}>
      <Spinner size={spinnerSize} color={spinnerColor} />
      {text && (
        <LoadingText size={size} overlay={overlay}>
          {text}
        </LoadingText>
      )}
    </LoadingContainer>
  );
};
