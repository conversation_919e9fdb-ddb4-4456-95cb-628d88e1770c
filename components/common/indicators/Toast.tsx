/**
 * Toast提示组件
 */

import React, { useEffect, useState } from 'react';
import { View, Text, XStack } from '@tamagui/core';
import { styled } from '@tamagui/core';

// Toast类型
export type ToastType = 'success' | 'error' | 'warning' | 'info';

// Toast位置
export type ToastPosition = 'top' | 'center' | 'bottom';

// Toast属性
export interface ToastProps {
  type?: ToastType;
  message: string;
  duration?: number;
  position?: ToastPosition;
  visible?: boolean;
  onHide?: () => void;
  icon?: React.ReactNode;
}

// 样式化Toast容器
const ToastContainer = styled(View, {
  name: 'ToastContainer',
  position: 'absolute',
  left: 16,
  right: 16,
  zIndex: 9999,
  
  variants: {
    position: {
      top: {
        top: 60,
      },
      
      center: {
        top: '50%',
        transform: [{ translateY: '-50%' }],
      },
      
      bottom: {
        bottom: 100,
      },
    },
  },
  
  defaultVariants: {
    position: 'top',
  },
});

const ToastContent = styled(XStack, {
  name: 'ToastContent',
  alignItems: 'center',
  gap: '$3',
  paddingHorizontal: '$4',
  paddingVertical: '$3',
  borderRadius: '$4',
  shadowColor: '$shadowColor',
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 4,
  elevation: 5,
  
  variants: {
    type: {
      success: {
        backgroundColor: '$success',
      },
      
      error: {
        backgroundColor: '$error',
      },
      
      warning: {
        backgroundColor: '$warning',
      },
      
      info: {
        backgroundColor: '$primary',
      },
    },
  },
  
  defaultVariants: {
    type: 'info',
  },
});

const ToastText = styled(Text, {
  name: 'ToastText',
  color: '$white',
  fontSize: '$4',
  fontWeight: '500',
  flex: 1,
  lineHeight: '$4',
});

const IconContainer = styled(View, {
  name: 'IconContainer',
  alignItems: 'center',
  justifyContent: 'center',
});

export const Toast: React.FC<ToastProps> = ({
  type = 'info',
  message,
  duration = 3000,
  position = 'top',
  visible = false,
  onHide,
  icon,
}) => {
  const [isVisible, setIsVisible] = useState(visible);

  useEffect(() => {
    setIsVisible(visible);
  }, [visible]);

  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onHide?.();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onHide]);

  if (!isVisible) {
    return null;
  }

  return (
    <ToastContainer position={position}>
      <ToastContent type={type}>
        {icon && (
          <IconContainer>
            {icon}
          </IconContainer>
        )}
        <ToastText>
          {message}
        </ToastText>
      </ToastContent>
    </ToastContainer>
  );
};

// Toast管理器（简化版）
export class ToastManager {
  private static instance: ToastManager;
  private toasts: Array<{ id: string; props: ToastProps }> = [];
  private listeners: Array<(toasts: Array<{ id: string; props: ToastProps }>) => void> = [];

  static getInstance(): ToastManager {
    if (!ToastManager.instance) {
      ToastManager.instance = new ToastManager();
    }
    return ToastManager.instance;
  }

  show(props: Omit<ToastProps, 'visible' | 'onHide'>): string {
    const id = Date.now().toString();
    const toast = {
      id,
      props: {
        ...props,
        visible: true,
        onHide: () => this.hide(id),
      },
    };

    this.toasts.push(toast);
    this.notifyListeners();

    return id;
  }

  hide(id: string): void {
    this.toasts = this.toasts.filter(toast => toast.id !== id);
    this.notifyListeners();
  }

  success(message: string, duration?: number): string {
    return this.show({ type: 'success', message, duration });
  }

  error(message: string, duration?: number): string {
    return this.show({ type: 'error', message, duration });
  }

  warning(message: string, duration?: number): string {
    return this.show({ type: 'warning', message, duration });
  }

  info(message: string, duration?: number): string {
    return this.show({ type: 'info', message, duration });
  }

  subscribe(listener: (toasts: Array<{ id: string; props: ToastProps }>) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener([...this.toasts]));
  }
}

// 导出单例实例
export const toast = ToastManager.getInstance();
