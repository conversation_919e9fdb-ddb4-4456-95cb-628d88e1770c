/**
 * 进度条组件
 */

import React from 'react';
import { View, Text, XStack, YStack } from '@tamagui/core';
import { styled } from '@tamagui/core';

// 进度条属性
export interface ProgressBarProps {
  value: number; // 0-100
  max?: number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  backgroundColor?: string;
  showLabel?: boolean;
  label?: string;
  animated?: boolean;
}

// 样式化进度条容器
const ProgressContainer = styled(YStack, {
  name: 'ProgressContainer',
  gap: '$2',
  width: '100%',
});

const ProgressTrack = styled(View, {
  name: 'ProgressTrack',
  backgroundColor: '$backgroundHover',
  borderRadius: '$2',
  overflow: 'hidden',
  
  variants: {
    size: {
      small: {
        height: 4,
      },
      
      medium: {
        height: 6,
      },
      
      large: {
        height: 8,
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const ProgressFill = styled(View, {
  name: 'ProgressFill',
  height: '100%',
  backgroundColor: '$primary',
  borderRadius: '$2',
  
  variants: {
    animated: {
      true: {
        animation: 'smooth',
        animationDuration: 300,
      },
    },
  },
});

const ProgressLabel = styled(XStack, {
  name: 'ProgressLabel',
  justifyContent: 'space-between',
  alignItems: 'center',
});

const LabelText = styled(Text, {
  name: 'LabelText',
  color: '$color',
  
  variants: {
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

const PercentageText = styled(Text, {
  name: 'PercentageText',
  color: '$placeholderColor',
  fontWeight: '500',
  
  variants: {
    size: {
      small: {
        fontSize: '$2',
      },
      
      medium: {
        fontSize: '$3',
      },
      
      large: {
        fontSize: '$4',
      },
    },
  },
  
  defaultVariants: {
    size: 'medium',
  },
});

export const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max = 100,
  size = 'medium',
  color,
  backgroundColor,
  showLabel = false,
  label,
  animated = true,
}) => {
  // 确保值在有效范围内
  const normalizedValue = Math.max(0, Math.min(value, max));
  const percentage = (normalizedValue / max) * 100;

  return (
    <ProgressContainer>
      {showLabel && (
        <ProgressLabel>
          {label && (
            <LabelText size={size}>
              {label}
            </LabelText>
          )}
          <PercentageText size={size}>
            {Math.round(percentage)}%
          </PercentageText>
        </ProgressLabel>
      )}
      
      <ProgressTrack 
        size={size}
        style={backgroundColor ? { backgroundColor } : undefined}
      >
        <ProgressFill
          animated={animated}
          style={{
            width: `${percentage}%`,
            backgroundColor: color || undefined,
          }}
        />
      </ProgressTrack>
    </ProgressContainer>
  );
};
