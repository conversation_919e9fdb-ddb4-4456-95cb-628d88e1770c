/**
 * 格式化工具函数单元测试
 */

import {
  maskPhone,
  maskIdCard,
  maskName,
  formatFileSize,
  formatNumber,
  formatPercentage,
  formatRoomNumber,
  formatRoomNumberShort,
  formatStatus,
  formatRelation,
  formatRegistrationMethod,
  formatNoticeCategory,
  formatAccessType,
  truncateText,
  capitalize,
  camelToKebab,
  kebabToCamel,
} from '../../lib/utils/format';

describe('Format Utils', () => {
  describe('maskPhone', () => {
    test('should mask phone numbers correctly', () => {
      expect(maskPhone('13800138000')).toBe('138****8000');
      expect(maskPhone('15912345678')).toBe('159****5678');
    });

    test('should return original value for invalid phone numbers', () => {
      expect(maskPhone('123')).toBe('123');
      expect(maskPhone('')).toBe('');
    });
  });

  describe('maskIdCard', () => {
    test('should mask ID card numbers correctly', () => {
      expect(maskIdCard('110101199001011234')).toBe('110101********1234');
    });

    test('should return original value for invalid ID cards', () => {
      expect(maskIdCard('123')).toBe('123');
      expect(maskIdCard('')).toBe('');
    });
  });

  describe('maskName', () => {
    test('should mask names correctly', () => {
      expect(maskName('张三')).toBe('张*');
      expect(maskName('李四五')).toBe('李*五');
      expect(maskName('王小明')).toBe('王*明');
      expect(maskName('欧阳修文')).toBe('欧**文');
    });

    test('should return original value for short names', () => {
      expect(maskName('李')).toBe('李');
      expect(maskName('')).toBe('');
    });
  });

  describe('formatFileSize', () => {
    test('should format file sizes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });

    test('should handle decimal places', () => {
      expect(formatFileSize(1536, 1)).toBe('1.5 KB');
      expect(formatFileSize(2097152, 0)).toBe('2 MB');
    });
  });

  describe('formatNumber', () => {
    test('should add thousand separators', () => {
      expect(formatNumber(1000)).toBe('1,000');
      expect(formatNumber(1234567)).toBe('1,234,567');
      expect(formatNumber(123)).toBe('123');
    });
  });

  describe('formatPercentage', () => {
    test('should calculate percentages correctly', () => {
      expect(formatPercentage(50, 100)).toBe('50.0%');
      expect(formatPercentage(33, 100, 2)).toBe('33.00%');
      expect(formatPercentage(1, 3, 1)).toBe('33.3%');
    });

    test('should handle zero total', () => {
      expect(formatPercentage(10, 0)).toBe('0%');
    });
  });

  describe('formatRoomNumber', () => {
    test('should format room numbers correctly', () => {
      expect(formatRoomNumber('1号楼', '1单元', '3楼', '302')).toBe('1号楼1单元3楼302');
      expect(formatRoomNumber('A栋', '2单元', '5楼', '501')).toBe('A栋2单元5楼501');
    });
  });

  describe('formatRoomNumberShort', () => {
    test('should format short room numbers correctly', () => {
      expect(formatRoomNumberShort('1号楼', '1单元', '3楼', '302')).toBe('1-1-302');
      expect(formatRoomNumberShort('2号楼', '3单元', '5楼', '501')).toBe('2-3-501');
    });
  });

  describe('formatStatus', () => {
    test('should format status correctly', () => {
      expect(formatStatus('verified')).toBe('已认证');
      expect(formatStatus('pending')).toBe('待审核');
      expect(formatStatus('rejected')).toBe('已拒绝');
      expect(formatStatus('published')).toBe('已发布');
      expect(formatStatus('unknown')).toBe('unknown');
    });
  });

  describe('formatRelation', () => {
    test('should format relations correctly', () => {
      expect(formatRelation('owner')).toBe('业主');
      expect(formatRelation('tenant')).toBe('租户');
      expect(formatRelation('family')).toBe('家属');
      expect(formatRelation('unknown')).toBe('unknown');
    });
  });

  describe('formatRegistrationMethod', () => {
    test('should format registration methods correctly', () => {
      expect(formatRegistrationMethod('property')).toBe('物业登记');
      expect(formatRegistrationMethod('self')).toBe('自主注册');
      expect(formatRegistrationMethod('unknown')).toBe('unknown');
    });
  });

  describe('formatNoticeCategory', () => {
    test('should format notice categories correctly', () => {
      expect(formatNoticeCategory('property_notice')).toBe('物业通知');
      expect(formatNoticeCategory('lost_found')).toBe('失物招领');
      expect(formatNoticeCategory('seeking_help')).toBe('寻求帮助');
      expect(formatNoticeCategory('idle_items')).toBe('闲置物品');
    });
  });

  describe('formatAccessType', () => {
    test('should format access types correctly', () => {
      expect(formatAccessType('password')).toBe('密码');
      expect(formatAccessType('face')).toBe('人脸');
      expect(formatAccessType('card')).toBe('门卡');
      expect(formatAccessType('qr_code')).toBe('二维码');
    });
  });

  describe('truncateText', () => {
    test('should truncate text correctly', () => {
      expect(truncateText('Hello World', 5)).toBe('Hello...');
      expect(truncateText('Short', 10)).toBe('Short');
      expect(truncateText('', 5)).toBe('');
    });
  });

  describe('capitalize', () => {
    test('should capitalize strings correctly', () => {
      expect(capitalize('hello')).toBe('Hello');
      expect(capitalize('WORLD')).toBe('World');
      expect(capitalize('tEST')).toBe('Test');
      expect(capitalize('')).toBe('');
    });
  });

  describe('camelToKebab', () => {
    test('should convert camelCase to kebab-case', () => {
      expect(camelToKebab('camelCase')).toBe('camel-case');
      expect(camelToKebab('someVariableName')).toBe('some-variable-name');
      expect(camelToKebab('HTML')).toBe('h-t-m-l');
    });
  });

  describe('kebabToCamel', () => {
    test('should convert kebab-case to camelCase', () => {
      expect(kebabToCamel('kebab-case')).toBe('kebabCase');
      expect(kebabToCamel('some-variable-name')).toBe('someVariableName');
      expect(kebabToCamel('test')).toBe('test');
    });
  });
});
