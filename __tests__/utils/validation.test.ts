/**
 * 验证工具函数单元测试
 */

import {
  validatePhone,
  validatePassword,
  validateVerificationCode,
  validateName,
  validateRoom,
  validateEmail,
  validateRequired,
  validateLength,
} from '../../lib/utils/validation';

describe('Validation Utils', () => {
  describe('validatePhone', () => {
    test('should validate correct phone numbers', () => {
      expect(validatePhone('13800138000')).toEqual({ isValid: true });
      expect(validatePhone('15912345678')).toEqual({ isValid: true });
      expect(validatePhone('18888888888')).toEqual({ isValid: true });
    });

    test('should reject invalid phone numbers', () => {
      expect(validatePhone('')).toEqual({ 
        isValid: false, 
        message: '请输入手机号' 
      });
      expect(validatePhone('1234567890')).toEqual({ 
        isValid: false, 
        message: '请输入正确的手机号格式' 
      });
      expect(validatePhone('12345678901')).toEqual({ 
        isValid: false, 
        message: '请输入正确的手机号格式' 
      });
    });
  });

  describe('validatePassword', () => {
    test('should validate strong passwords', () => {
      const result = validatePassword('Abc123456');
      expect(result.isValid).toBe(true);
      expect(result.strength).toBe('strong');
    });

    test('should validate medium passwords', () => {
      const result = validatePassword('abc123');
      expect(result.isValid).toBe(true);
      expect(result.strength).toBe('medium');
    });

    test('should reject weak passwords', () => {
      expect(validatePassword('')).toEqual({ 
        isValid: false, 
        message: '请输入密码' 
      });
      expect(validatePassword('123')).toEqual({ 
        isValid: false, 
        message: '密码长度至少6位',
        strength: 'weak'
      });
      expect(validatePassword('123456')).toEqual({ 
        isValid: false, 
        message: '密码必须包含字母和数字',
        strength: 'weak'
      });
    });
  });

  describe('validateVerificationCode', () => {
    test('should validate correct verification codes', () => {
      expect(validateVerificationCode('123456')).toEqual({ isValid: true });
      expect(validateVerificationCode('000000')).toEqual({ isValid: true });
    });

    test('should reject invalid verification codes', () => {
      expect(validateVerificationCode('')).toEqual({ 
        isValid: false, 
        message: '请输入验证码' 
      });
      expect(validateVerificationCode('12345')).toEqual({ 
        isValid: false, 
        message: '验证码格式错误，应为6位数字' 
      });
      expect(validateVerificationCode('12345a')).toEqual({ 
        isValid: false, 
        message: '验证码格式错误，应为6位数字' 
      });
    });
  });

  describe('validateName', () => {
    test('should validate correct names', () => {
      expect(validateName('张三')).toEqual({ isValid: true });
      expect(validateName('李四五')).toEqual({ isValid: true });
      expect(validateName('王小明')).toEqual({ isValid: true });
    });

    test('should reject invalid names', () => {
      expect(validateName('')).toEqual({ 
        isValid: false, 
        message: '请输入姓名' 
      });
      expect(validateName('a')).toEqual({ 
        isValid: false, 
        message: '姓名至少2个字符' 
      });
      expect(validateName('a'.repeat(21))).toEqual({ 
        isValid: false, 
        message: '姓名不能超过20个字符' 
      });
      expect(validateName('张三@')).toEqual({ 
        isValid: false, 
        message: '姓名不能包含特殊字符' 
      });
    });
  });

  describe('validateRoom', () => {
    test('should validate correct room numbers', () => {
      expect(validateRoom('101')).toEqual({ isValid: true });
      expect(validateRoom('1001')).toEqual({ isValid: true });
      expect(validateRoom('2305')).toEqual({ isValid: true });
    });

    test('should reject invalid room numbers', () => {
      expect(validateRoom('')).toEqual({ 
        isValid: false, 
        message: '请输入房间号' 
      });
      expect(validateRoom('10')).toEqual({ 
        isValid: false, 
        message: '房间号格式错误，应为3-4位数字' 
      });
      expect(validateRoom('10a')).toEqual({ 
        isValid: false, 
        message: '房间号格式错误，应为3-4位数字' 
      });
    });
  });

  describe('validateEmail', () => {
    test('should validate correct emails', () => {
      expect(validateEmail('<EMAIL>')).toEqual({ isValid: true });
      expect(validateEmail('<EMAIL>')).toEqual({ isValid: true });
    });

    test('should reject invalid emails', () => {
      expect(validateEmail('')).toEqual({ 
        isValid: false, 
        message: '请输入邮箱' 
      });
      expect(validateEmail('invalid-email')).toEqual({ 
        isValid: false, 
        message: '请输入正确的邮箱格式' 
      });
      expect(validateEmail('@domain.com')).toEqual({ 
        isValid: false, 
        message: '请输入正确的邮箱格式' 
      });
    });
  });

  describe('validateRequired', () => {
    test('should validate non-empty values', () => {
      expect(validateRequired('value', '字段')).toEqual({ isValid: true });
      expect(validateRequired(123, '数字')).toEqual({ isValid: true });
      expect(validateRequired(true, '布尔值')).toEqual({ isValid: true });
    });

    test('should reject empty values', () => {
      expect(validateRequired('', '字段')).toEqual({ 
        isValid: false, 
        message: '请输入字段' 
      });
      expect(validateRequired(null, '字段')).toEqual({ 
        isValid: false, 
        message: '请输入字段' 
      });
      expect(validateRequired(undefined, '字段')).toEqual({ 
        isValid: false, 
        message: '请输入字段' 
      });
    });
  });

  describe('validateLength', () => {
    test('should validate correct length strings', () => {
      expect(validateLength('hello', 3, 10, '字段')).toEqual({ isValid: true });
      expect(validateLength('test', 4, 4, '字段')).toEqual({ isValid: true });
    });

    test('should reject incorrect length strings', () => {
      expect(validateLength('', 3, 10, '字段')).toEqual({ 
        isValid: false, 
        message: '请输入字段' 
      });
      expect(validateLength('ab', 3, 10, '字段')).toEqual({ 
        isValid: false, 
        message: '字段至少3个字符' 
      });
      expect(validateLength('a'.repeat(11), 3, 10, '字段')).toEqual({ 
        isValid: false, 
        message: '字段不能超过10个字符' 
      });
    });
  });
});
