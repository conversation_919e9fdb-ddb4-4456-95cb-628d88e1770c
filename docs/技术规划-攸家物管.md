# 技术规划文档 - 攸家物管 App V2.1.0

## 版本信息
- **项目名称**: 攸家物管 App
- **版本**: V2.1.0
- **创建日期**: 2024-12-19
- **技术栈**: React Native + Expo

## 1. 技术栈选择

### 1.1 核心框架
- **React Native**: 0.73.x (最新稳定版)
- **Expo SDK**: 50.x (最新稳定版)
- **TypeScript**: 5.x (类型安全)

### 1.2 状态管理
- **Zustand**: 轻量级状态管理，适合中小型应用
  - 简单易用，学习成本低
  - TypeScript 支持良好
  - 无需复杂的 boilerplate 代码

### 1.3 导航方案
- **React Navigation v6**: 
  - `@react-navigation/native`
  - `@react-navigation/bottom-tabs` (底部导航)
  - `@react-navigation/stack` (页面栈导航)
  - `@react-navigation/native-stack` (原生栈导航，性能更好)

### 1.4 UI 组件库
- **tamagui**: 
  - 组件丰富，定制性强
  - 支持主题系统
  - TypeScript 支持良好
- **React Native Elements**: 作为补充组件库
- **React Native Vector Icons**: 图标库

### 1.5 网络请求
- **Axios**: HTTP 客户端
- **React Query (TanStack Query)**: 数据获取和缓存
  - 自动缓存和同步
  - 错误处理和重试机制
  - 离线支持

### 1.6 表单处理
- **React Hook Form**: 高性能表单库
- **Yup**: 表单验证

### 1.7 数据存储
- **AsyncStorage**: 本地数据存储
- **Expo SecureStore**: 敏感数据存储 (Token等)
- **MMKV**: 高性能键值存储 (可选)

### 1.8 设备功能
- **Expo Camera**: 拍照功能 (人像录入)
- **Expo ImagePicker**: 图片选择
- **Expo Contacts**: 联系人功能
- **React Native NFC Manager**: NFC 门卡录入
- **Expo Linking**: 拨号功能

### 1.9 推送通知
- **Expo Notifications**: 推送通知
- **Firebase Cloud Messaging**: 消息推送服务

### 1.10 其他工具库
- **date-fns**: 日期处理
- **react-native-image-crop-picker**: 图片裁剪
- **react-native-permissions**: 权限管理
- **react-native-keychain**: 安全存储
- **flipper**: 调试工具

## 2. 项目架构设计

### 2.1 目录结构规划

```
src/
├── components/           # 通用组件
│   ├── common/          # 基础组件
│   ├── forms/           # 表单组件
│   └── business/        # 业务组件
├── screens/             # 页面组件
│   ├── auth/           # 认证相关页面
│   ├── resident/       # 住户管理
│   ├── staff/          # 员工管理
│   ├── access/         # 门禁管理
│   ├── statistics/     # 登记统计
│   ├── logs/           # 门禁日志
│   ├── notice/         # 通知公告
│   ├── phone/          # 电话管理
│   ├── profile/        # 个人中心
│   └── settings/       # 设置
├── navigation/          # 导航配置
├── services/           # API 服务
├── stores/             # 状态管理
├── utils/              # 工具函数
├── constants/          # 常量定义
├── types/              # TypeScript 类型定义
├── hooks/              # 自定义 Hooks
└── assets/             # 静态资源
    ├── images/
    ├── icons/
    └── fonts/
```

### 2.2 代码组织方式

#### 2.2.1 命名规范
- **文件名**: kebab-case (例: `resident-list.tsx`)
- **组件名**: PascalCase (例: `ResidentList`)
- **变量/函数**: camelCase (例: `getUserInfo`)
- **常量**: UPPER_SNAKE_CASE (例: `API_BASE_URL`)
- **类型定义**: PascalCase + Type/Interface 后缀

#### 2.2.2 模块划分策略
- **按功能模块划分**: 每个业务模块独立目录
- **分层架构**: UI层、业务逻辑层、数据层分离
- **组件复用**: 通用组件与业务组件分离

## 3. 页面和路由规划

### 3.1 导航结构

```
App
├── AuthStack (认证栈)
│   ├── Login (登录)
│   ├── BindAdmin (绑定管理员)
│   └── BindSuccess (绑定成功)
└── MainStack (主应用栈)
    └── BottomTabs (底部导航)
        ├── ResidentTab (住户管理)
        ├── OrganizationTab (组织发展)
        ├── AccessTab (门禁管理)
        ├── StatisticsTab (登记统计)
        ├── LogsTab (门禁日志)
        ├── NoticeTab (通知公告)
        ├── PhoneTab (电话管理)
        └── ProfileTab (个人中心)
```

### 3.2 页面详细规划

#### 3.2.1 认证模块 (AuthStack)
- **LoginScreen**: 登录页面
- **BindAdminScreen**: 绑定管理员
- **BindSuccessScreen**: 绑定成功提示

#### 3.2.2 住户管理模块 (ResidentTab)
- **ResidentListScreen**: 住户列表 (已认证/待认证/未通过)
- **AddResidentScreen**: 新增住户
- **EditResidentScreen**: 编辑住户信息
- **ResidentDetailScreen**: 住户详情

#### 3.2.3 组织发展模块 (OrganizationTab)
- **StaffListScreen**: 员工列表
- **AddStaffScreen**: 新增员工
- **EditStaffScreen**: 编辑员工
- **PositionListScreen**: 岗位列表
- **AddPositionScreen**: 新增岗位
- **EditPositionScreen**: 编辑岗位

#### 3.2.4 门禁管理模块 (AccessTab)
- **AccessManagementScreen**: 个人门禁管理
- **SetPasswordScreen**: 设置密码
- **RecordCardScreen**: 录入门卡
- **RecordFaceScreen**: 录入人像
- **CameraScreen**: 拍照界面

#### 3.2.5 其他模块
- **StatisticsScreen**: 登记统计
- **LogsScreen**: 门禁日志
- **NoticeListScreen**: 通知公告列表
- **PublishNoticeScreen**: 发布通知
- **NoticeDetailScreen**: 通知详情
- **PhoneListScreen**: 电话管理
- **AddPhoneScreen**: 添加电话
- **ProfileScreen**: 个人中心
- **SettingsScreen**: 设置页面

## 4. 组件设计

### 4.1 通用组件 (Common Components)

#### 4.1.1 基础组件
- **CustomButton**: 自定义按钮
- **CustomInput**: 自定义输入框
- **CustomModal**: 自定义弹窗
- **LoadingSpinner**: 加载指示器
- **EmptyState**: 空状态组件
- **ErrorBoundary**: 错误边界组件

#### 4.1.2 布局组件
- **Screen**: 页面容器
- **Header**: 页面头部
- **TabBar**: 自定义标签栏
- **Card**: 卡片组件
- **List**: 列表组件
- **Grid**: 网格组件

#### 4.1.3 表单组件
- **FormInput**: 表单输入框
- **FormSelect**: 下拉选择
- **FormDatePicker**: 日期选择器
- **FormImagePicker**: 图片选择器
- **FormValidation**: 表单验证

### 4.2 业务组件 (Business Components)

#### 4.2.1 住户相关
- **ResidentCard**: 住户卡片
- **ResidentStatusBadge**: 认证状态标签
- **ResidentFilter**: 住户筛选器
- **RoomSelector**: 房间选择器

#### 4.2.2 员工相关
- **StaffCard**: 员工卡片
- **PositionSelector**: 岗位选择器
- **PermissionSelector**: 权限选择器

#### 4.2.3 门禁相关
- **AccessStatusIndicator**: 门禁状态指示器
- **NFCReader**: NFC 读卡器
- **FaceCapture**: 人脸采集
- **PasswordInput**: 密码输入器

#### 4.2.4 通知相关
- **NoticeCard**: 通知卡片
- **NoticeEditor**: 通知编辑器
- **CommentList**: 评论列表

### 4.3 组件层级关系

```
App
├── Navigation Components
├── Screen Components
│   ├── Layout Components
│   ├── Business Components
│   └── Common Components
└── Utility Components
```

## 5. 开发计划

### 5.1 开发阶段划分

#### 阶段一：项目初始化和基础架构 (1-2周)
- Expo 项目创建和配置
- 基础目录结构搭建
- 核心依赖包安装和配置
- 导航系统搭建
- 状态管理配置
- API 服务层搭建
- 通用组件开发

#### 阶段二：认证和用户管理 (1-2周)
- 登录功能实现
- 管理员绑定功能
- 用户状态管理
- Token 管理和刷新
- 权限控制系统

#### 阶段三：核心业务功能 (3-4周)
- 住户管理模块
- 员工管理模块
- 门禁管理模块
- 基础 CRUD 操作
- 数据同步机制

#### 阶段四：高级功能 (2-3周)
- 人像录入 (相机集成)
- NFC 门卡录入
- 通知公告系统
- 文件上传功能
- 推送通知

#### 阶段五：完善和优化 (1-2周)
- 统计和日志功能
- 电话管理
- 个人中心
- 设置功能
- 性能优化
- 错误处理完善

#### 阶段六：测试和发布 (1-2周)
- 单元测试
- 集成测试
- 用户体验测试
- 性能测试
- 打包和发布

### 5.2 关键功能点和技术难点

#### 5.2.1 技术难点
1. **NFC 集成**: React Native NFC Manager 配置和使用
2. **相机功能**: 人脸识别和图片处理
3. **权限管理**: 复杂的角色权限控制
4. **数据同步**: 离线数据和在线同步
5. **性能优化**: 大列表渲染和图片加载优化

#### 5.2.2 关键功能点
1. **多状态管理**: 住户认证状态流转
2. **文件上传**: 图片和视频上传处理
3. **实时通信**: 推送通知和消息同步
4. **数据筛选**: 复杂的筛选和搜索功能
5. **表单验证**: 多步骤表单和验证

### 5.3 风险评估和应对策略

#### 5.3.1 技术风险
- **NFC 兼容性**: 不同设备 NFC 功能差异
- **相机权限**: iOS/Android 权限处理差异
- **性能问题**: 大数据量列表性能

#### 5.3.2 应对策略
- 充分的设备测试
- 权限处理的统一封装
- 虚拟化列表和懒加载
- 错误边界和降级方案

## 6. 总结

本技术规划基于 Expo 生态系统，选择了成熟稳定的技术栈，采用模块化架构设计，确保代码的可维护性和可扩展性。开发计划分为6个阶段，循序渐进地实现所有功能需求。

重点关注用户体验、性能优化和代码质量，为后续的功能迭代和维护打下坚实基础。
