{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "paths": {"@/*": ["./*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/services/*": ["./src/services/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["./src/constants/*"], "@/types/*": ["./src/types/*"], "@/hooks/*": ["./src/hooks/*"], "@/navigation/*": ["./src/navigation/*"], "@/assets/*": ["./src/assets/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "src/**/*"], "exclude": ["node_modules", "dist", "build", ".expo"]}