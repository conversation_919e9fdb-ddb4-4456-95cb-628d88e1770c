{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/services/*": ["./lib/services/*"], "@/stores/*": ["./lib/stores/*"], "@/utils/*": ["./lib/utils/*"], "@/types/*": ["./lib/types/*"], "@/constants/*": ["./constants/*"], "@/hooks/*": ["./hooks/*"], "@/assets/*": ["./assets/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "src/**/*"], "exclude": ["node_modules", "dist", "build", ".expo"]}