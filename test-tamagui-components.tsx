/**
 * Tamagui组件测试文件
 * 用于验证所有基础组件是否正常工作
 */

import React from 'react';
import { Text, View } from 'tamagui';

// 导入按钮组件
import {
    CustomButton,
    FloatingActionButton,
    IconButton
} from './components/common/buttons';

// 导入指示器组件
import {
    EmptyState,
    LoadingSpinner,
    ProgressBar,
    StatusBadge
} from './components/common/indicators';

// 导入输入组件
import {
    CustomInput,
    PasswordInput,
    SearchInput
} from './components/common/inputs';

export const TamaguiComponentsTest: React.FC = () => {
  return (
    <View padding="$4" gap="$4">
      <Text fontSize="$6" fontWeight="bold">
        Tamagui 基础组件测试
      </Text>
      
      {/* 按钮组件测试 */}
      <View gap="$2">
        <Text fontSize="$4" fontWeight="600">按钮组件</Text>
        <CustomButton variant="primary" size="medium">
          主要按钮
        </CustomButton>
        <IconButton 
          icon={<Text>🏠</Text>} 
          variant="default" 
          size="medium" 
        />
        <FloatingActionButton 
          icon={<Text>+</Text>} 
          size="medium" 
          position="bottom-right" 
        />
      </View>
      
      {/* 指示器组件测试 */}
      <View gap="$2">
        <Text fontSize="$4" fontWeight="600">指示器组件</Text>
        <LoadingSpinner size="medium" />
        <EmptyState 
          title="暂无数据" 
          description="请稍后再试" 
          size="medium" 
        />
        <StatusBadge 
          status="success" 
          text="成功" 
          size="medium" 
        />
        <ProgressBar 
          value={60} 
          size="medium" 
          showLabel 
          label="进度" 
        />
      </View>
      
      {/* 输入组件测试 */}
      <View gap="$2">
        <Text fontSize="$4" fontWeight="600">输入组件</Text>
        <CustomInput 
          label="用户名" 
          placeholder="请输入用户名" 
          size="medium" 
        />
        <SearchInput 
          placeholder="搜索..." 
          size="medium" 
        />
        <PasswordInput 
          label="密码" 
          placeholder="请输入密码" 
          size="medium" 
          showStrengthIndicator 
        />
      </View>
    </View>
  );
};

export default TamaguiComponentsTest;
